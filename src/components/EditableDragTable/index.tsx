import type { ProColumns } from '@ant-design/pro-components';
import { DragSortTable } from '@ant-design/pro-components';
import { arrayMove } from '@dnd-kit/sortable';
import { useIntl } from '@umijs/max';
import { Form } from 'antd';
import React, { useCallback, useEffect, useImperativeHandle, useRef, useState } from 'react';

export interface EditableDragTableProps {
    columns: ProColumns<any>[];
    dataSource?: any[];
    loading?: boolean;
    onDataChange?: (data: any[]) => void;
    rowKey?: string;
    dragSortKey?: string;
    maxHeight?: number;
    readonly?: boolean;
}

export interface EditableDragTableRef {
    validateAndGetData: () => Promise<{ success: boolean; data?: any[]; errors?: any[] }>;
    getSelectedRows: () => any[];
    clearSelection: () => void;
    resetData: (data: any[]) => void;
}

const EditableDragTable = React.forwardRef<EditableDragTableRef, EditableDragTableProps>(
    ({ columns, dataSource = [], loading = false, onDataChange, rowKey = 'id', dragSortKey = 'sortOrder', maxHeight = 600, readonly = false, ...tableProps }, ref) => {
        const intl = useIntl();
        const [form] = Form.useForm();
        const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
        const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
        const [tableData, setTableData] = useState<any[]>([]);
        const editingRowsRef = useRef<Map<React.Key, any>>(new Map());

        // 处理数据源更新
        useEffect(() => {
            if (dataSource && dataSource.length > 0) {
                // 为每行数据生成唯一 key
                const dataWithKeys = dataSource.map((item, index) => ({
                    ...item,
                    [rowKey]: item[rowKey] || `row_${index}`,
                }));

                setTableData(dataWithKeys);

                // 设置所有可编辑行为编辑状态
                const editableRowKeys = dataWithKeys
                    .map(item => item[rowKey])
                    .filter(key => key !== undefined);
                setEditableRowKeys(editableRowKeys);
            } else {
                setTableData([]);
                setEditableRowKeys([]);
            }
        }, [dataSource, rowKey]);

        // 处理行数据变化
        const handleValuesChange = useCallback((changedValues: any, allValues: any) => {
            const { [rowKey]: recordKey, ...values } = changedValues;
            if (recordKey) {
                editingRowsRef.current.set(recordKey, values);
            }
        }, []);

        // 处理拖拽排序
        const handleDragSortEnd = useCallback((oldIndex: number,
            newIndex: number,
            newDataSource: any,
        ) => {
            if (oldIndex !== newIndex) {
                const newItems = arrayMove(dataSource, oldIndex, newIndex).map((item, idx) => ({
                    ...item,
                    [dragSortKey]: idx + 1, // 更新排序字段
                }));
                setTableData(newItems);
                onDataChange?.(newItems);
            }
        }, [dataSource, onDataChange]);

        // 处理选择变化
        const handleSelectionChange = useCallback((keys: React.Key[], rows: any[]) => {
            setSelectedRowKeys(keys);
        }, []);

        // 暴露方法给父组件
        useImperativeHandle(ref, () => ({
            async validateAndGetData() {
                try {
                    const results = await form.validateFields()
                    // console.log('results', results);
                    // if (results.errorFields.length > 0) {
                    //     message.error(intl.formatMessage({ id: 'common.message.editableTable.inputError' }));
                    //     return { success: false, errors: results.errorFields };
                    // }

                    // 合并原始数据和编辑数据
                    const finalData = tableData.map(row => {
                        const editedData = editingRowsRef.current.get(row[rowKey]);
                        return editedData ? { ...row, ...editedData } : row;
                    });

                    return { success: true, data: finalData };
                } catch (error) {
                    console.error('Validation error:', error);
                    return { success: false, errors: [error] };
                }
            },

            getSelectedRows() {
                return tableData.filter(row => selectedRowKeys.includes(row[rowKey]));
            },

            clearSelection() {
                setSelectedRowKeys([]);
            },

            resetData(data: any[]) {
                const dataWithKeys = data.map((item, index) => ({
                    ...item,
                    [rowKey]: item[rowKey] || `row_${index}`,
                }));

                setTableData(dataWithKeys);
                editingRowsRef.current.clear();
                setSelectedRowKeys([]);
            },
        }), [tableData, selectedRowKeys, rowKey, form]);

        return (
            <Form
                form={form}
                component={false}
                onValuesChange={handleValuesChange}
            >
                <DragSortTable<any>
                    columns={columns}
                    rowKey={rowKey}
                    dataSource={tableData}
                    loading={loading}
                    editable={{
                        type: 'multiple',
                        editableKeys: readonly ? [] : editableKeys,
                        form,
                        onValuesChange: handleValuesChange,
                    }}
                    rowSelection={{
                        selectedRowKeys,
                        onChange: handleSelectionChange,
                        preserveSelectedRowKeys: true,
                    }}
                    dragSortKey={dragSortKey}
                    onDragSortEnd={handleDragSortEnd}
                    scroll={{
                        y: maxHeight,
                        x: 'max-content'
                    }}
                    pagination={false}
                    size="small"
                    options={false}
                    search={false}
                    toolBarRender={false}
                    // 性能优化配置
                    virtual={tableData.length > 100}
                    // sticky={{ offsetHeader: 0 }}
                    tableAlertRender={() => false}
                    {...tableProps}
                />
            </Form>
        );
    }
);

EditableDragTable.displayName = 'EditableDragTable';

export default EditableDragTable;