import { UserCouponStatus } from "./Coupon";

export interface UserCouponEntity {
    userCouponId: number;
    couponId: number;
    couponName: string;
    beginTime: string;
    deadline: string;
    cstId: number;
    customerName: string;
    couponStatus: UserCouponStatus;
    couponStatusDesc: string;
    getPerson: string;
    getTime: string;
    useTime: string;
    useOrderNo: string;
}

export type UserCouponDataRequest = {
    pageNo: number;
    pageSize: number;
    getBegainTime?: string;
    getEndTime?: string;
    useBeaginTime?: string;
    useEndTime?: string;
} & Pick<UserCouponEntity, 'cstId' | 'couponId' | 'couponName'>

