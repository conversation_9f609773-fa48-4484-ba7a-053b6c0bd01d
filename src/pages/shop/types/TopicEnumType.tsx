import { FormattedMessage } from "@umijs/max";

export enum TopicStatus {
    DRAFT = 0,
    PUBLISHED = 1,
}

export enum TopicPageSource {
    TOPIC = 1,
    APP = 2,
}

export const TopicStatusEnum = {
    [TopicStatus.DRAFT]: { text: <FormattedMessage id="shop.topic.topicStatus.draft" />, status: 'Warning' },
    [TopicStatus.PUBLISHED]: { text: <FormattedMessage id="shop.topic.topicStatus.published" />, status: 'Success' },
};

export const TopicPageSourceEnum = {
    [TopicPageSource.TOPIC]: { text: <FormattedMessage id="shop.topic.topicPageSource.topic" />, status: 'Success' },
    [TopicPageSource.APP]: { text: <FormattedMessage id="shop.topic.topicPageSource.app" />, status: 'Success' },
};
