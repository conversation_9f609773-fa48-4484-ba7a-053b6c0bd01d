import dayjs from "dayjs";
import { CouponStatus, CouponValidityType } from "./Coupon";

export interface CouponDataRequest {
    id: number;
    ids: number[];
    couponStatus: CouponStatus;
    couponName: string;
}

export interface UpdateCouponStatusRequest {
    id: number;
    couponStatus: CouponStatus;
}

export interface SendCouponRequest {
    id: number;
    cstIds: string[];
}

interface CategoryItem {
    categoryId: string;
    categoryName: string;
}

interface BrandItem {
    brandId: string;
    brandName: string;
}

export interface ItemItem {
    itemId: string;
    itemName: string;
    brandName: string;
    categoryName: string;
    salePrice: string;
}

/**
 * 添加优惠券表单值
 */
export interface CouponFormValues {
    couponName: string;
    totalStock: number;
    enableAmount: number;
    validityType: CouponValidityType;
    validityTime?: [dayjs.Dayjs, dayjs.Dayjs];
    validityDays?: number;
    useDesc?: string;
    remark?: string;
    goodsRangeType: 0 | 1; // 0: 指定商品, 1: 全部商品
    itemScopeType?: 1 | 4; // 1: 指定商品, 4: 指定类目+品牌 
    scopeItemList?: ItemItem[];
    scopeCategoryIdList?: string[];
    scopeBrandIdList?: string[];
    scopeCategoryList?: CategoryItem[];
    scopeBrandList?: BrandItem[];
}


export type CouponFormRequest = Omit<CouponFormValues, 'validityTime' | 'scopeCategoryIdList' | 'scopeBrandIdList' | 'scopeItemList'> & {
    validityBeginTime?: string;
    validityEndTime?: string;
    scopeItemList?: Pick<ItemItem, 'itemId'>[];
}
