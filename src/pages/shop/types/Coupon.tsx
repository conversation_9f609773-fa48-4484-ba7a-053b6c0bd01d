import { FormattedMessage } from "@umijs/max";

export enum CouponStatus {
    WAIT = 1,
    ACTIVE = 2,
    INVALID = 3,
}

export enum CouponValidityType {
    ABSOLUTE = 1,
    RELATIVE = 2,
}

export const CouponStatusEnum = {
    [CouponStatus.WAIT]: { text: <FormattedMessage id="shop.coupon.couponStatus.wait" />, status: 'Warning' },
    [CouponStatus.ACTIVE]: { text: <FormattedMessage id="shop.coupon.couponStatus.active" />, status: 'Success' },
    [CouponStatus.INVALID]: { text: <FormattedMessage id="shop.coupon.couponStatus.invalid" />, status: 'Error' },
};

// export const CouponValidityTypeEnum = {
//     [CouponValidityType.ABSOLUTE]: { text: '绝对时间', status: 'Success' },
//     [CouponValidityType.RELATIVE]: { text: '相对时间', status: 'Success' },
// };

export interface CouponEntity {
    id?: number;
    couponName?: string;
    remark?: string;
    couponAmount?: string;
    validityType?: number;
    enableAmount?: string;
    totalStock?: number;
    useableStock?: number;
    validityDays?: number;
    validityBeginTime?: string;
    validityEndTime?: string;
    updatePerson?: string;
    updateTime?: string;
    couponStatus?: CouponStatus;
    validityValue?: string;
}



// 1.已领取未用 2.已使用 3.过期 4.已作废
export enum UserCouponStatus {
    USED = 1,
    VALID = 2,
    EXPIRED = 3,
    INVALID = 4,
}

export const UserCouponStatusEnum = {
    [UserCouponStatus.USED]: { text: <FormattedMessage id="shop.couponRecord.list.used" />, status: 'Success' },
    [UserCouponStatus.VALID]: { text: <FormattedMessage id="shop.couponRecord.list.valid" />, status: 'Warning' },
    [UserCouponStatus.EXPIRED]: { text: <FormattedMessage id="shop.couponRecord.list.expired" />, status: 'Error' },
    [UserCouponStatus.INVALID]: { text: <FormattedMessage id="shop.couponRecord.list.invalid" />, status: 'Error' },
};

