import { ActivityProgress, ActivityStatus, ActivityType } from "./ActivityEnum";

export interface ActivityEntity {
    activityId?: number;
    activityName?: string;
    activityType?: ActivityType;
    activityStatus?: ActivityStatus;
    activityStep?: ActivityProgress;
    startTime?: string;
    endTime?: string;
    remark?: string;
    updatePerson?: string;
    updateTime?: string;
}

export type ActivityDataRequest = {
    ids?: number[];
    pageNo?: number;
    pageSize?: number;
} & Pick<ActivityEntity, 'activityId' | 'activityName' | 'activityType' | 'activityStatus' | 'activityStep' | 'updatePerson'>

export type ActivityItemEntity = {
    activityId?: number;
    itemId?: number;
    itemSn?: string
    itemName?: string;
    categoryId?: number;
    categoryName?: string;
    brandId?: number;
    brandName?: string;
    promotionPrice?: number;
    minBuyNum?: number;
    maxBuyNum?: number;
    totalMaxBuyNum?: number;
    buySelfNum?: number;
    giftSelfNum?: number;
    giftTotalMaxBuyNum?: number;
    showOrder?: number;
}

export interface ActivityGiftItem {
    activityId?: number;
    itemId?: number;
    itemSn?: string;
    itemName?: string;
    categoryId?: number;
    categoryName?: string;
    brandId?: number;
    brandName?: string;
    giftNum?: number;
    giftTotalMaxBuyNum?: number;
}

export enum GiftRuleType {
    /**
     * 满元
     */
    FULL_AMOUNT = 1,
    /**
     * 满件
     */
    FULL_ITEM = 2,
}

interface ActivityLadderRule {
    ladder?: number;
    giftRuleType?: GiftRuleType;
    activityId?: number;
    enableAmount?: number;
    activityGiftItems?: ActivityGiftItem[];
}

export type ActivityFormValues = {
    activityId?: number;
    activityName?: string;
    activityType?: ActivityType;
    activityStatus?: ActivityStatus;
    activityStep?: ActivityProgress;
    startTime?: string;
    endTime?: string;
    activityDesc?: string;
    activityArea?: number;
    areaId?: string;
    activityImage?: string;
    userCoupon?: number;
    onCredit?: number;
    orderNoStock?: number;
    remark?: string;
    giftRuleType?: GiftRuleType;
    activityItems?: ActivityItemEntity[];
    activityLadderRules?: ActivityLadderRule[];
}

export type GiftSettingsFormValues = {
    giftRuleMode: ActivityType.EVERY_FULL_GIFT | ActivityType.LADDER_FULL_GIFT;
} & Pick<ActivityFormValues, 'activityLadderRules' | 'giftRuleType'>
