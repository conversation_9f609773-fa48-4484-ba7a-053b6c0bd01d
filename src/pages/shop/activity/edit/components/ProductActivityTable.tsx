import EditableDragTable, { EditableDragTableProps, EditableDragTableRef } from '@/components/EditableDragTable';
import SelectProductDrawer from '@/pages/shop/topic/decoration/CraftEditor/components/LinkSettings/SelectProductDrawer';
import { ActivityItemEntity } from '@/pages/shop/types/ActivityDataType';
import { useSearchParams } from '@umijs/max';
import { Button, Space, message } from 'antd';
import { uniqBy } from 'lodash';
import React, { useState } from 'react'; // 导入 useEffect
import { useIntl } from 'react-intl';


interface ProductActivityTableProps extends EditableDragTableProps {
    tableRef?: EditableDragTableRef;
    dataSource: ActivityItemEntity[];
}
const ProductActivityTable: React.FC<ProductActivityTableProps> = (props) => {
    const { dataSource = [], onDataChange, columns, tableRef } = props;

    const [searchParams] = useSearchParams();
    const type = searchParams.get('type'); // view | edit | copy
    const readonly = type === 'view';

    const intl = useIntl();
    const [isModalVisible, setIsModalVisible] = useState(false);


    // 添加商品
    const handleAddProduct = (ids, selectedProducts) => {
        console.log('selectedProducts', selectedProducts);
        const newItems = selectedProducts.filter(item => item?.itemId).map(item => ({
            itemId: item.itemId,
            itemSn: item.itemSn,
            itemName: item.itemName,
            categoryId: item.categoryId,
            categoryName: item.categoryName,
            brandId: item.brandId,
            brandName: item.brandName,
            suggestPrice: item.suggestPrice,
            promotionPrice: undefined,
            minBuyNum: undefined,
            maxBuyNum: undefined,
            totalMaxBuyNum: undefined,
            buySelfNum: undefined,
            giftSelfNum: undefined,
            giftTotalMaxBuyNum: undefined,
        }));
        const updatedItems = uniqBy([...newItems, ...dataSource].map((item, index) => ({ ...item, showOrder: index + 1 })), 'itemId');
        onDataChange?.(updatedItems);
        setIsModalVisible(false);
    };

    // 删除选中商品
    const handleDeleteSelected = () => {
        const selectedRows = tableRef?.current?.getSelectedRows();
        if (selectedRows.length === 0) {
            message.warning(intl.formatMessage({ id: 'common.message.needSelectOne' }));
            return;
        }
        const newDataSource = dataSource.filter(
            item => !selectedRows.some(selected => selected.itemId === item.itemId)
        );
        onDataChange?.(newDataSource);
        tableRef?.current?.clearSelection();
    };

    return (
        <>
            {!readonly && <Space className="mb-4">
                <Button type="primary" onClick={() => setIsModalVisible(true)}>
                    {intl.formatMessage({ id: 'shop.common.button.addProduct' })}
                </Button>
                <Button className='button-outline' onClick={handleDeleteSelected}>
                    {intl.formatMessage({ id: 'common.button.delete' })}
                </Button>
                <Button className='button-outline'>{intl.formatMessage({ id: 'common.button.batchImport' })}</Button>
            </Space>}

            <EditableDragTable
                ref={tableRef}
                columns={columns}
                dataSource={dataSource}
                onDataChange={onDataChange}
                rowKey="itemId"
                maxHeight={600}
                dragSortKey='showOrder'
                readonly={readonly}
            />

            <SelectProductDrawer
                selected={dataSource.map(item => item.itemId)}
                visible={isModalVisible} onClose={() => {
                    setIsModalVisible(false);
                }} onOk={handleAddProduct} />
        </>
    );
};

export default ProductActivityTable;