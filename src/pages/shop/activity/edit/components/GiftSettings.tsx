import { ActivityGiftItem, GiftRuleType, GiftSettingsFormValues } from '@/pages/shop/types/ActivityDataType';
import { ActivityType } from '@/pages/shop/types/ActivityEnum';
import { DeleteOutlined } from '@ant-design/icons';
import {
    ProForm,
    ProFormDependency,
    ProFormDigit,
    ProFormItem,
    ProFormList,
    ProFormRadio
} from '@ant-design/pro-components';
import { Button, FormInstance, message, Popconfirm, Tag } from 'antd';
import React from 'react';
import { useIntl } from 'react-intl';
import GiftItemsTable from './GiftItemsTable';

// 定义数据结构
// 阶梯规则
interface ActivityLadderRule {
    ladder: number; // 阶梯序号 (通常在 ProFormList 内部管理)
    giftRuleType: GiftRuleType; // 1:满元, 2:满件
    enableAmount: number; // 满X元 or 满X件
    activityGiftItems: ActivityGiftItem[];
}

interface GiftSettingsProps {
    giftRuleMode: ActivityType;
    form?: FormInstance;
}

const GiftSettings: React.FC<GiftSettingsProps> = (props) => {
    const { form } = props;
    const intl = useIntl();

    return (
        <ProForm<GiftSettingsFormValues>
            form={form}
            submitter={false}
            initialValues={{
                giftRuleMode: props.giftRuleMode,
                giftRuleType: GiftRuleType.FULL_ITEM,
                activityLadderRules: [
                    {
                        ladder: 1,
                        enableAmount: undefined,
                        activityGiftItems: [],
                    },
                ],
            }}
        >
            {/* 赠送规则 */}
            <ProFormRadio.Group
                noStyle
                hidden
                name="giftRuleMode"
                label={intl.formatMessage({ id: 'shop.activity.giftInfo.giftRule' })}
                options={[
                    { label: intl.formatMessage({ id: 'shop.activity.giftInfo.perFull' }), value: ActivityType.EVERY_FULL_GIFT },
                    { label: intl.formatMessage({ id: 'shop.activity.giftInfo.tieredGifting' }), value: ActivityType.LADDER_FULL_GIFT },
                ]}

            />

            {/* 赠送条件 */}
            <ProFormRadio.Group
                name="giftRuleType"
                label={intl.formatMessage({ id: 'shop.activity.giftInfo.giftCondition' })}
                options={[
                    { label: intl.formatMessage({ id: 'shop.activity.giftInfo.fullItems' }), value: 2 }, // 2:满件
                    { label: intl.formatMessage({ id: 'shop.activity.giftInfo.fullAmount' }), value: 1 }, // 1:满元
                ]}
                rules={[
                    { required: true },
                ]}
            />

            <ProFormDependency name={['giftRuleMode', 'giftRuleType']}>
                {({ giftRuleMode, giftRuleType }) => {
                    const isTiered = giftRuleMode === ActivityType.LADDER_FULL_GIFT;
                    const isFullAmount = giftRuleType === GiftRuleType.FULL_AMOUNT;
                    return (
                        <ProFormList<ActivityLadderRule>
                            name="activityLadderRules"
                            // 总是至少有一个规则，无论“每满赠”还是“阶梯赠”
                            min={1}
                            // “每满赠”模式下，最多只能有一个规则
                            max={isTiered ? undefined : 1}
                            // 创建按钮只在“阶梯赠”模式下显示
                            creatorButtonProps={
                                isTiered
                                    ? {
                                        position: 'bottom',
                                        creatorButtonText: intl.formatMessage({ id: 'shop.activity.giftInfo.addLadder' }),
                                        record: () => ({
                                            // 新增阶梯时，根据当前阶梯数量自动递增 ladder 值
                                            ladder: (form?.getFieldValue('activityLadderRules')?.length || 0) + 1,
                                            enableAmount: undefined,
                                            activityGiftItems: [],
                                        }),
                                    }
                                    : false
                            }
                            copyIconProps={false} // 不显示复制按钮
                            deleteIconProps={false} // 不使用ProFormList自带的删除按钮
                        >
                            {/* ProFormList 内部的每一项（即每个阶梯或每满赠的唯一规则）的表单字段 */}
                            {(f, index, action) => (
                                <>
                                    {/* 赠送条件 (满件/满元) */}
                                    <div className="flex justify-between">
                                        <ProFormDigit
                                            name="enableAmount"
                                            addonBefore={
                                                <>{
                                                    isTiered && <Tag color="red">{intl.formatMessage({ id: 'shop.activity.giftInfo.ladder' })}{index + 1}</Tag>
                                                }</>
                                            }
                                            addonAfter={
                                                isFullAmount
                                                    ? intl.formatMessage({ id: 'shop.activity.giftInfo.conditionUnitAmount' })
                                                    : intl.formatMessage({ id: 'shop.activity.giftInfo.conditionUnitItems' })
                                            }
                                            min={0}
                                            fieldProps={{ precision: isFullAmount ? 2 : 0 }}
                                            rules={[{ required: true, message: intl.formatMessage({ id: 'shop.activity.giftInfo.enableAmountRequired' }) }]}
                                            className="flex-grow"
                                        />
                                        {isTiered && <Popconfirm
                                            title={intl.formatMessage({ id: 'common.confirm.delete' })}
                                            onConfirm={() => {
                                                const currentRules = form?.getFieldValue('activityLadderRules') || [];
                                                if (currentRules.length > 1) { // 确保至少保留一个阶梯
                                                    action.remove(index);
                                                } else {
                                                    message.warning(intl.formatMessage({ id: 'shop.activity.giftInfo.ladderMin' }));
                                                }
                                            }}
                                        >
                                            <Button type="link" danger icon={<DeleteOutlined />} />
                                        </Popconfirm>}
                                    </div>

                                    {/* 赠品列表 - 使用提取出的 GiftItemsTable 组件 */}
                                    <ProFormItem
                                        name="activityGiftItems"
                                        rules={[
                                            {
                                                required: true,
                                                message: intl.formatMessage({ id: 'shop.activity.giftInfo.giftItemsRequired' }),
                                                validator: (_, value) =>
                                                    value && value.length > 0 ? Promise.resolve() : Promise.reject(),
                                            },
                                        ]}
                                    >
                                        <GiftItemsTable
                                            // 通过 form.getFieldValue 获取当前 ProFormList 项的赠品数据
                                            dataSource={form?.getFieldValue(['activityLadderRules', index, 'activityGiftItems']) || []}
                                            onChange={(newItems) => {
                                                // 当 GiftItemsTable 数据变化时，手动更新 ProForm 的对应字段
                                                // 确保数据双向绑定
                                                form?.setFieldsValue({
                                                    activityLadderRules: form?.getFieldValue('activityLadderRules')?.map((rule, ruleIndex) =>
                                                        ruleIndex === index ? { ...rule, activityGiftItems: newItems } : rule
                                                    )
                                                });
                                            }}
                                        />
                                    </ProFormItem>
                                </>
                            )
                            }
                        </ProFormList>
                    );
                }}
            </ProFormDependency >
        </ProForm >
    );
};

export default GiftSettings;