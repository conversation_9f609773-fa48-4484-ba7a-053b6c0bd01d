import { FormattedMessage } from "@umijs/max";

const commonColumn = [
    {
        title: <FormattedMessage id="goods.list.table.itemSn" />,
        dataIndex: 'itemId',
        ellipsis: true,
        width: 80,
        editable: false,
    },
    {
        title: <FormattedMessage id="goods.list.table.itemName" />,
        dataIndex: 'itemName',
        ellipsis: true,
        width: 80,
        editable: false,
    },
    {
        title: <FormattedMessage id="goods.list.table.category" />,
        dataIndex: 'categoryName',
        width: 120,
        editable: false,
    },
    {
        title: <FormattedMessage id="goods.list.table.brand" />,
        dataIndex: 'brandName',
        width: 120,
        editable: false,
    },
    {
        title: <FormattedMessage id="goods.list.table.suggestPrice" />,
        dataIndex: 'suggestPrice',
        valueType: 'money',
        width: 120,
        editable: false,
    },
    {
        title: <>
            <FormattedMessage id="shop.activity.products.activityPrice" />
            <span className='text-red-500'>*</span>
        </>,
        dataIndex: 'promotionPrice',
        valueType: 'digit',
        width: 120,
        formItemProps: {
            rules: [
                {
                    required: true,
                    message: <FormattedMessage id="shop.activity.products.required" />,
                },
                {
                    min: 0,
                    type: 'number',
                    message: <FormattedMessage id="shop.activity.products.numberMinZero" />,
                },
            ],
        },
    },
]

const minBuyNum = {
    title: <FormattedMessage id="shop.activity.products.minOrderQuantityPerCustomer" />,
    dataIndex: 'minBuyNum',
    valueType: 'digit',
    width: 150,
    fieldProps: {
        precision: 0,
    },
    formItemProps: {
        rules: [
            {
                min: 0,
                type: 'number',
                message: <FormattedMessage id="shop.activity.products.numberMinZero" />,
            },
        ],
    },
}

const maxBuyNum = {
    title: <FormattedMessage id="shop.activity.products.maxPurchaseQuantityPerCustomer" />,
    dataIndex: 'maxBuyNum',
    valueType: 'digit',
    fieldProps: {
        precision: 0,
    },
    width: 150,
    formItemProps: {
        rules: [
            {
                min: 0,
                type: 'number',
                message: <FormattedMessage id="shop.activity.products.numberMinZero" />,
            },
        ],
    },
}

const totalMaxBuyNum = {
    title: <FormattedMessage id="shop.activity.products.totalPurchaseQuantity" />,
    dataIndex: 'totalMaxBuyNum',
    valueType: 'digit',
    fieldProps: {
        precision: 0,
    },
    width: 120,
    formItemProps: {
        rules: [
            {
                min: 0,
                type: 'number',
                message: <FormattedMessage id="shop.activity.products.numberMinZero" />,
            },
        ],
    },
}

// 购本品数
const purchasedQuantity = {
    title: <FormattedMessage id="shop.activity.products.purchasedQuantity" />,
    dataIndex: 'purchasedQuantity',
    valueType: 'digit',
    width: 80,
    fieldProps: {
        precision: 0,
    },
    formItemProps: {
        rules: [
            {
                min: 0,
                type: 'number',
                message: <FormattedMessage id="shop.activity.products.numberMinZero" />,
            },
        ],
    },
}

// 赠本品数
const giftQuantity = {
    title: <FormattedMessage id="shop.activity.products.giftQuantity" />,
    dataIndex: 'giftQuantity',
    valueType: 'digit',
    width: 80,
    fieldProps: {
        precision: 0,
    },
    formItemProps: {
        rules: [
            {
                min: 0,
                type: 'number',
                message: <FormattedMessage id="shop.activity.products.numberMinZero" />,
            },
        ],
    },
}

// 商品总限购数
const totalMaxPurchaseQuantity = {
    title: <FormattedMessage id="shop.activity.products.totalMaxPurchaseQuantity" />,
    dataIndex: 'totalMaxPurchaseQuantity',
    valueType: 'digit',
    width: 80,
    fieldProps: {
        precision: 0,
    },
    formItemProps: {
        rules: [
            {
                min: 0,
                type: 'number',
                message: <FormattedMessage id="shop.activity.products.numberMinZero" />,
            },
        ],
    },
}

// 赠品总限购数
const totalMaxGiftQuantity = {
    title: <FormattedMessage id="shop.activity.products.totalMaxGiftQuantity" />,
    dataIndex: 'totalMaxGiftQuantity',
    valueType: 'digit',
    width: 80,
    fieldProps: {
        precision: 0,
    },
    formItemProps: {
        rules: [
            {
                min: 0,
                type: 'number',
                message: <FormattedMessage id="shop.activity.products.numberMinZero" />,
            },
        ],
    },
}



const sortOrder = {
    title: <FormattedMessage id="shop.activity.products.sortOrder" />,
    dataIndex: 'showOrder',
    editable: false,
    width: 50,
}

export const specialActivityColumns = [
    sortOrder,
    ...commonColumn,
    minBuyNum,
    maxBuyNum,
    totalMaxBuyNum,
];

export const buyGiftSelfActivityColumns = [
    sortOrder,
    ...commonColumn,
    minBuyNum,
    maxBuyNum,
    purchasedQuantity,
    giftQuantity,
    totalMaxPurchaseQuantity,
    totalMaxGiftQuantity,
]

export const fullGiftActivityColumns = [
    sortOrder,
    ...commonColumn,
    minBuyNum,
    maxBuyNum,
    totalMaxBuyNum,
]