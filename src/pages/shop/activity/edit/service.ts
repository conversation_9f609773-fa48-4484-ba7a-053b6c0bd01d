import { request } from '@/utils/request';
import { ActivityFormValues, ActivityItemEntity } from '../../types/ActivityDataType';

// 模拟数据源，用于演示
// 调整为150条数据，以更好地演示虚拟滚动
const MOCK_LONG_PRODUCTS: ActivityItemEntity[] = Array.from({ length: 150 }, (_, i) => ({
    id: `mock_${i + 1}`,
    itemId: `SP${1000 + i}`,
    itemName: `博世刹车油-${i + 1}L`,
    categoryName: `刹车油`,
    brandName: `博世/BOSCH`,
    suggestPrice: parseFloat((Math.random() * 50 + 20).toFixed(2)),
    promotionPrice: parseFloat((Math.random() * 40 + 10).toFixed(2)),
    minBuyNum: Math.floor(Math.random() * 5) + 1,
    maxBuyNum: Math.floor(Math.random() * 10) + 5,
    totalMaxBuyNum: Math.floor(Math.random() * 100) + 50,
    showOrder: i + 1,
}));

const mockSpecial = () => {
    return {
        "activityId": 1223,
        "activityName": "特价活动",
        "activityType": 4,
        "startTime": "2025-05-11 00:00:00",
        "endTime": "2025-05-15 00:00:00",
        "activityDesc": "活动说明",
        "activityArea": 0,//活动人群 0不限，1指定标签客户
        "areaId": "1,2,3,4",
        "activityImage": "https://ncz-upload.carzone365.com/bwork/2024/11/19/d0da280692fa4b15a6d2c3db3d0f3294.GIF",
        "userCoupon": 0,
        "onCredit": 1,
        "orderNoStock": 1,
        "remark": "活动备注",
        "activityItems": [
            {
                "activityId": 122,
                "itemId": 1232222,
                "itemName": "商品名称1",
                "categoryId": 123,
                "categoryName": "类目名称",
                "brandId": 3333,
                "brandName": "品牌名称",
                "promotionPrice": 2399,
                "minBuyNum": 1,//单客户起订量
                "maxBuyNum": 3,//单客户限购
                "totalMaxBuyNum": 300,//总限购
                "showOrder": 1
            },
            {
                "activityId": 122,
                "itemId": 1232223,
                "itemName": "商品名称2",
                "categoryId": 123,
                "categoryName": "类目名称",
                "brandId": 3333,
                "brandName": "品牌名称",
                "promotionPrice": 2399,
                "minBuyNum": 1,//单客户起订量
                "maxBuyNum": 3,//单客户限购
                "totalMaxBuyNum": 300,//总限购
                "showOrder": 1
            }
        ]
    }
}

export const addActivity = async (params: ActivityFormValues) => {
    return request<boolean>(
        '/ipmspromotion/activityManager/addActivity',
        {
            data: params,
        },
    );
}

export const editActivity = async (params: ActivityFormValues) => {
    return request<boolean>(
        '/ipmspromotion/activityManager/editActivity',
        {
            data: params,
        },
    );
}

export const queryActivityById = async (params: { activityId: number }) => {
    return request<ActivityFormValues>(
        '/ipmspromotion/activityQuery/queryActivityById',
        {
            data: params,
        },
    ).then(res => {
        return mockSpecial()
    })
}