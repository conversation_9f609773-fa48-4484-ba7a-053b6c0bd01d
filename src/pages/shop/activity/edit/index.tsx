import AuthButton from "@/components/common/AuthButton";
import { PageContainer } from "@ant-design/pro-components";
import { history, useIntl, useSearchParams } from "@umijs/max";
import { Button, Space } from "antd";
import { ActivityType } from "../../types/ActivityEnum";
import BuyGiftActivtyForm from "./activity/BuyGift";
import FullGift from "./activity/FullGift";
import SpecialActivityForm from "./activity/SpecialPrice";
import { addActivity, editActivity } from "./service";

export interface ActivityFormProps {
    onSubmit: (params: any) => Promise<any>;
}

export const SubmitArea: React.FC<{
    readonly: boolean;
    submit: () => void;
    reset: () => void;
}> = (props) => {
    const { readonly } = props;
    const intl = useIntl();
    return <div
        className="bg-white p-4 mt-4 flex justify-end rounded-lg"
    >
        <Space>
            {
                readonly ?
                    <Button onClick={() => history.back()}>
                        {intl.formatMessage({ id: 'common.button.close' })}
                    </Button> :
                    <>
                        <Button onClick={() => props.reset?.()}>
                            {intl.formatMessage({ id: 'common.button.cancel' })}
                        </Button>
                        <AuthButton authority="saveActivity" type="primary" onClick={() => props.submit?.()}>
                            {intl.formatMessage({ id: 'common.button.save' })}
                        </AuthButton>
                    </>
            }
        </Space>
    </div>
}

const ActivityForm: React.FC = () => {
    const [searchParams] = useSearchParams();
    const activityType = Number(searchParams.get('activityType'));
    const activityId = searchParams.get('id');


    const onSubmit = async (params) => {
        return new Promise((resolve) => {
            if (activityId) {
                editActivity(params).then(result => {
                    resolve(result);
                })
            } else {
                addActivity(params).then(result => {
                    resolve(result);
                })
            }
        }).then((result) => {
            console.log('result', result);
            history.push('/shop/activity/list');
        })

    }

    return <PageContainer>
        {
            activityType == ActivityType.SPECIAL_PRICE && <SpecialActivityForm onSubmit={onSubmit} />
        }
        {
            activityType == ActivityType.BUY_GIFT_SELF && <BuyGiftActivtyForm onSubmit={onSubmit} />
        }
        {
            [ActivityType.LADDER_FULL_GIFT, ActivityType.EVERY_FULL_GIFT].includes(activityType) && <FullGift onSubmit={onSubmit} />
        }
    </PageContainer>
}

export default ActivityForm;