import { useNode } from '@craftjs/core';
import { Button, Form, Upload } from 'antd'; // Added AntTooltip
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useIntl } from 'react-intl';
import { v4 as uuidv4 } from 'uuid';
import LinkSettings, { LinkType, LinkTypeValue } from '../LinkSettings';

interface HotspotData {
    id: string;
    x: number; // 0-1
    y: number; // 0-1
    width: number; // 0-1
    height: number; // 0-1
    tooltip?: string;
    linkTarget?: LinkTypeValue;
}

type ResizeHandleType = 'tl' | 'tr' | 'bl' | 'br';

const RESIZE_HANDLE_SIZE = 6; // px
const MIN_HOTSPOT_DIMENSION = 0.01; // 1% of image size for manual resize/draw
const DEFAULT_NEW_HOTSPOT_WIDTH = 0.1; // 10%
const DEFAULT_NEW_HOTSPOT_HEIGHT = 0.05; // 5% (was 20% - 5% might fit better)
const PLACEMENT_SEARCH_STEP = 0.02; // Search in 2% steps

const ImageHotspotSettings = () => {
    const {
        actions: { setProp },
        imageUrl,
        hotspots = []
    } = useNode(node => ({
        imageUrl: node.data.props.imageUrl,
        hotspots: node.data.props.hotspots || [],
        id: node.id
    }));

    const imagePreviewRef = useRef<HTMLImageElement>(null);
    const [isDrawing, setIsDrawing] = useState(false);
    const [startCoords, setStartCoords] = useState({ x: 0, y: 0 });
    const [currentRect, setCurrentRect] = useState<Omit<HotspotData, 'id' | 'tooltip' | 'link' | 'productId'> | null>(null);

    const [selectedHotspotId, setSelectedHotspotId] = useState<string | null>(null);
    const [isDraggingHotspot, setIsDraggingHotspot] = useState(false);
    const [dragStartOffset, setDragStartOffset] = useState({ x: 0, y: 0 });

    const [isResizingHotspot, setIsResizingHotspot] = useState(false);
    const [activeResizeHandle, setActiveResizeHandle] = useState<ResizeHandleType | null>(null);
    const [resizeStartHotspot, setResizeStartHotspot] = useState<HotspotData | null>(null);

    const intl = useIntl();

    // Effect to handle clicks outside the image container
    useEffect(() => {
        const handleGlobalMouseDown = (event: MouseEvent) => {
            // Check if the click target is outside the image container
            if (imagePreviewRef.current && !imagePreviewRef.current.parentElement?.contains(event.target as Node)) {
                setSelectedHotspotId(null);
            }
        };

        // Add the global event listener
        document.addEventListener('mousedown', handleGlobalMouseDown);

        // Cleanup function to remove the event listener
        return () => {
            document.removeEventListener('mousedown', handleGlobalMouseDown);
        };
    }, [imagePreviewRef, setSelectedHotspotId]); // Depend on imagePreviewRef and setSelectedHotspotId


    const getRelativeCoords = useCallback((event: MouseEvent | React.MouseEvent) => { /* ... unchanged ... */
        if (!imagePreviewRef.current) return null;
        const rect = imagePreviewRef.current.getBoundingClientRect();
        const imgWidth = imagePreviewRef.current.offsetWidth;
        const imgHeight = imagePreviewRef.current.offsetHeight;
        if (imgWidth === 0 || imgHeight === 0) return null;
        const x = Math.max(0, Math.min(event.clientX - rect.left, imgWidth));
        const y = Math.max(0, Math.min(event.clientY - rect.top, imgHeight));
        return {
            relativeX: x / imgWidth,
            relativeY: y / imgHeight,
            imgWidth,
            imgHeight,
            absoluteX: x,
            absoluteY: y,
        };
    }, [imagePreviewRef]);

    const handleImageMouseDownForDrawing = (event: React.MouseEvent) => { /* ... unchanged ... */
        if ((event.target as HTMLElement).closest('.existing-hotspot-preview') || (event.target as HTMLElement).closest('.resize-handle')) {
            return;
        }
        if (!imageUrl) return;
        const coords = getRelativeCoords(event);
        if (!coords) return;
        setIsDrawing(true);
        setIsDraggingHotspot(false);
        setIsResizingHotspot(false);
        setStartCoords({ x: coords.relativeX, y: coords.relativeY });
        setCurrentRect({ x: coords.relativeX, y: coords.relativeY, width: 0, height: 0 });
        setSelectedHotspotId(null);
    };
    useEffect(() => { /* Drawing new hotspots - unchanged */
        const handleMouseMoveForDrawing = (event: MouseEvent) => {
            if (!isDrawing || !imagePreviewRef.current || !currentRect) return;
            const coords = getRelativeCoords(event);
            if (!coords) return;

            const newX = Math.min(startCoords.x, coords.relativeX);
            const newY = Math.min(startCoords.y, coords.relativeY);
            const newWidth = Math.abs(coords.relativeX - startCoords.x);
            const newHeight = Math.abs(coords.relativeY - startCoords.y);
            setCurrentRect({ x: newX, y: newY, width: newWidth, height: newHeight });
        };
        const handleMouseUpForDrawing = () => {
            if (!isDrawing || !currentRect) {
                setIsDrawing(false);
                return;
            }
            setIsDrawing(false);

            if (currentRect.width < MIN_HOTSPOT_DIMENSION || currentRect.height < MIN_HOTSPOT_DIMENSION) {
                setCurrentRect(null);
                return;
            }
            const newHotspot: HotspotData = {
                id: uuidv4(),
                ...currentRect,
                tooltip: `${intl.formatMessage({ id: 'topic.decoration.imageHotspot.hotspot' })} ${hotspots.length + 1}`,
                linkTarget: { type: LinkType.None, value: '' }
            };
            setProp((props: { hotspots: HotspotData[] }) => props.hotspots = [...props.hotspots, newHotspot], 500);
            setCurrentRect(null);
            setSelectedHotspotId(newHotspot.id);
        };

        if (isDrawing) {
            document.addEventListener('mousemove', handleMouseMoveForDrawing);
            document.addEventListener('mouseup', handleMouseUpForDrawing);
        }
        return () => {
            document.removeEventListener('mousemove', handleMouseMoveForDrawing);
            document.removeEventListener('mouseup', handleMouseUpForDrawing);
        };
    }, [isDrawing, startCoords, currentRect, getRelativeCoords, setProp, hotspots.length]);

    const handleHotspotMouseDownForDragging = (event: React.MouseEvent, hotspot: HotspotData) => { /* ... unchanged ... */
        event.stopPropagation();
        if (isDrawing) setIsDrawing(false);
        if (isResizingHotspot) setIsResizingHotspot(false);

        setSelectedHotspotId(hotspot.id);
        setIsDraggingHotspot(true);
        const coords = getRelativeCoords(event);
        if (!coords) return;
        setDragStartOffset({ x: coords.relativeX - hotspot.x, y: coords.relativeY - hotspot.y });
    };
    useEffect(() => { /* Dragging existing hotspots - unchanged */
        const selectedHotspot = hotspots.find((h: HotspotData) => h.id === selectedHotspotId);
        const handleMouseMoveForDragging = (event: MouseEvent) => {
            if (!isDraggingHotspot || !selectedHotspot || !imagePreviewRef.current) return;
            const coords = getRelativeCoords(event);
            if (!coords) return;
            let newX = coords.relativeX - dragStartOffset.x;
            let newY = coords.relativeY - dragStartOffset.y;
            newX = Math.max(0, Math.min(newX, 1 - selectedHotspot.width));
            newY = Math.max(0, Math.min(newY, 1 - selectedHotspot.height));
            if (newX !== selectedHotspot.x || newY !== selectedHotspot.y) {
                setProp((props: { hotspots: HotspotData[] }) => {
                    props.hotspots = props.hotspots.map(h =>
                        h.id === selectedHotspotId ? { ...h, x: newX, y: newY } : h
                    );
                }, 20);
            }
        };
        const handleMouseUpForDragging = () => setIsDraggingHotspot(false);
        if (isDraggingHotspot && selectedHotspot) {
            document.addEventListener('mousemove', handleMouseMoveForDragging);
            document.addEventListener('mouseup', handleMouseUpForDragging);
        }
        return () => {
            document.removeEventListener('mousemove', handleMouseMoveForDragging);
            document.removeEventListener('mouseup', handleMouseUpForDragging);
        };
    }, [isDraggingHotspot, selectedHotspotId, hotspots, dragStartOffset, setProp, getRelativeCoords]);

    const handleResizeHandleMouseDown = (event: React.MouseEvent, handleType: ResizeHandleType) => { /* ... unchanged ... */
        event.stopPropagation();
        if (isDrawing) setIsDrawing(false);
        if (isDraggingHotspot) setIsDraggingHotspot(false);
        const currentHotspot = hotspots.find((h: HotspotData) => h.id === selectedHotspotId);
        if (!currentHotspot) return;
        setIsResizingHotspot(true);
        setActiveResizeHandle(handleType);
        setResizeStartHotspot({ ...currentHotspot });
    };
    useEffect(() => { /* Resizing existing hotspots - unchanged */
        if (!isResizingHotspot || !activeResizeHandle || !resizeStartHotspot || !imagePreviewRef.current) {
            return;
        }
        const handleMouseMoveForResizing = (event: MouseEvent) => {
            const coords = getRelativeCoords(event);
            if (!coords) return;
            let { x, y, width, height } = resizeStartHotspot;
            if (activeResizeHandle === 'tl') {
                const newX = Math.min(coords.relativeX, resizeStartHotspot.x + resizeStartHotspot.width - MIN_HOTSPOT_DIMENSION);
                const newY = Math.min(coords.relativeY, resizeStartHotspot.y + resizeStartHotspot.height - MIN_HOTSPOT_DIMENSION);
                width = resizeStartHotspot.x + resizeStartHotspot.width - newX;
                height = resizeStartHotspot.y + resizeStartHotspot.height - newY;
                x = newX;
                y = newY;
            }
            else if (activeResizeHandle === 'tr') {
                const newY = Math.min(coords.relativeY, resizeStartHotspot.y + resizeStartHotspot.height - MIN_HOTSPOT_DIMENSION);
                width = coords.relativeX - resizeStartHotspot.x;
                height = resizeStartHotspot.y + resizeStartHotspot.height - newY;
                y = newY;
            }
            else if (activeResizeHandle === 'bl') {
                const newX = Math.min(coords.relativeX, resizeStartHotspot.x + resizeStartHotspot.width - MIN_HOTSPOT_DIMENSION);
                width = resizeStartHotspot.x + resizeStartHotspot.width - newX;
                height = coords.relativeY - resizeStartHotspot.y;
                x = newX;
            }
            else if (activeResizeHandle === 'br') {
                width = coords.relativeX - resizeStartHotspot.x;
                height = coords.relativeY - resizeStartHotspot.y;
            }
            x = Math.max(0, Math.min(x, 1 - MIN_HOTSPOT_DIMENSION));
            y = Math.max(0, Math.min(y, 1 - MIN_HOTSPOT_DIMENSION));
            width = Math.max(MIN_HOTSPOT_DIMENSION, Math.min(width, 1 - x));
            height = Math.max(MIN_HOTSPOT_DIMENSION, Math.min(height, 1 - y));
            setProp((props: { hotspots: HotspotData[] }) => {
                props.hotspots = props.hotspots.map(h =>
                    h.id === selectedHotspotId ? { ...h, x, y, width, height } : h
                );
            }, 20);
        };
        const handleMouseUpForResizing = () => {
            setIsResizingHotspot(false);
            setActiveResizeHandle(null);
            setResizeStartHotspot(null);
        };
        document.addEventListener('mousemove', handleMouseMoveForResizing);
        document.addEventListener('mouseup', handleMouseUpForResizing);
        return () => {
            document.removeEventListener('mousemove', handleMouseMoveForResizing);
            document.removeEventListener('mouseup', handleMouseUpForResizing);
        };
    }, [isResizingHotspot, activeResizeHandle, resizeStartHotspot, getRelativeCoords, setProp, selectedHotspotId]);

    const updateSelectedHotspotProp = (propName: keyof HotspotData, value: any) => { /* ... unchanged ... */
        if (!selectedHotspotId) return;
        setProp((props: { hotspots: HotspotData[] }) => {
            props.hotspots = props.hotspots.map(h =>
                h.id === selectedHotspotId ? { ...h, [propName]: value } : h
            );
        }, 500);
    };
    const updateSelectedHotspotNumberProp = (propName: keyof HotspotData, valueString: string | number | null) => { /* ... unchanged ... */
        if (!selectedHotspotId || valueString === null) return;
        const value = parseFloat(String(valueString));
        if (isNaN(value)) return;
        const clampedValue = (propName === 'x' || propName === 'y' || propName === 'width' || propName === 'height')
            ? Math.max(0, Math.min(1, value))
            : value;
        updateSelectedHotspotProp(propName, clampedValue);
    };
    const deleteHotspot = (idToDelete: string) => { /* ... unchanged ... */
        setProp((props: { hotspots: HotspotData[] }) => {
            props.hotspots = props.hotspots.filter(h => h.id !== idToDelete);
        }, 500);
        if (selectedHotspotId === idToDelete) {
            setSelectedHotspotId(null);
        }
    };
    const currentSelectedHotspotData = hotspots.find(h => h.id === selectedHotspotId);
    const handleUploadChange = (info: any) => { /* ... unchanged ... */
        if (info.file.status === 'done') {
            const url = info.file?.response?.data?.[0];
            if (url) {
                setProp((props: { imageUrl: string, hotspots: HotspotData[] }) => {
                    props.imageUrl = url;
                    props.hotspots = [];
                }, 500);
                setSelectedHotspotId(null);
                setIsDrawing(false);
                setCurrentRect(null);
            }
        } else if (info.file.status === 'error') {
            console.error("Upload error:", info.file.error);
        }
    };
    const renderResizeHandles = (hotspot: HotspotData) => { /* ... unchanged ... */
        if (!imageUrl || hotspot.id !== selectedHotspotId) return null;
        const handleStyleBase: React.CSSProperties = {
            position: 'absolute', width: `${RESIZE_HANDLE_SIZE}px`, height: `${RESIZE_HANDLE_SIZE}px`,
            background: 'white', border: '1px solid #155bd4', borderRadius: '1px', zIndex: 11,
        };
        const handles: { type: ResizeHandleType; style: React.CSSProperties }[] = [
            { type: 'tl', style: { ...handleStyleBase, top: `-${RESIZE_HANDLE_SIZE / 2}px`, left: `-${RESIZE_HANDLE_SIZE / 2}px`, cursor: 'nwse-resize' } },
            { type: 'tr', style: { ...handleStyleBase, top: `-${RESIZE_HANDLE_SIZE / 2}px`, right: `-${RESIZE_HANDLE_SIZE / 2}px`, cursor: 'nesw-resize' } },
            { type: 'bl', style: { ...handleStyleBase, bottom: `-${RESIZE_HANDLE_SIZE / 2}px`, left: `-${RESIZE_HANDLE_SIZE / 2}px`, cursor: 'nesw-resize' } },
            { type: 'br', style: { ...handleStyleBase, bottom: `-${RESIZE_HANDLE_SIZE / 2}px`, right: `-${RESIZE_HANDLE_SIZE / 2}px`, cursor: 'nwse-resize' } },
        ];
        return handles.map(handle => (
            <div key={handle.type} className="resize-handle" style={handle.style} onMouseDown={(e) => handleResizeHandleMouseDown(e, handle.type)} />
        ));
    };

    // --- NEW: Function to add hotspot programmatically ---
    const checkOverlap = (rect1: Omit<HotspotData, 'id'>, rect2: Omit<HotspotData, 'id'>): boolean => {
        return (
            rect1.x < rect2.x + rect2.width &&
            rect1.x + rect1.width > rect2.x &&
            rect1.y < rect2.y + rect2.height &&
            rect1.y + rect1.height > rect2.y
        );
    };

    const findEmptySpot = (
        defaultWidth: number,
        defaultHeight: number
    ): { x: number; y: number } => {
        if (!imagePreviewRef.current) return { x: 0, y: 0 }; // Fallback if image not loaded

        const maxAttemptsX = Math.floor((1 - defaultWidth) / PLACEMENT_SEARCH_STEP);
        const maxAttemptsY = Math.floor((1 - defaultHeight) / PLACEMENT_SEARCH_STEP);

        for (let j = 0; j <= maxAttemptsY; j++) {
            const yPos = j * PLACEMENT_SEARCH_STEP;
            if (yPos + defaultHeight > 1) break; // Ensure it fits vertically

            for (let i = 0; i <= maxAttemptsX; i++) {
                const xPos = i * PLACEMENT_SEARCH_STEP;
                if (xPos + defaultWidth > 1) break; // Ensure it fits horizontally

                const candidateRect = { x: xPos, y: yPos, width: defaultWidth, height: defaultHeight };
                let overlaps = false;
                for (const existingHotspot of hotspots) {
                    if (checkOverlap(candidateRect, existingHotspot)) {
                        overlaps = true;
                        break;
                    }
                }
                if (!overlaps) {
                    return { x: xPos, y: yPos };
                }
            }
        }
        // Fallback: if no spot found after thorough search, place it at 0,0 or slightly offset
        // For simplicity, let's try a small offset from 0,0 if (0,0) itself is taken.
        const fallbackCandidate = { x: 0, y: 0, width: defaultWidth, height: defaultHeight };
        let overlapsAtZero = false;
        for (const existingHotspot of hotspots) {
            if (checkOverlap(fallbackCandidate, existingHotspot)) {
                overlapsAtZero = true;
                break;
            }
        }
        if (!overlapsAtZero) return { x: 0, y: 0 };

        // If (0,0) taken, try small offset. This could be smarter.
        return { x: PLACEMENT_SEARCH_STEP, y: PLACEMENT_SEARCH_STEP };
    };


    const handleAddHotspotClick = () => {
        if (!imageUrl) {
            // Optionally show a message that image is required
            return;
        }

        const { x, y } = findEmptySpot(DEFAULT_NEW_HOTSPOT_WIDTH, DEFAULT_NEW_HOTSPOT_HEIGHT);

        const newHotspot: HotspotData = {
            id: uuidv4(),
            x,
            y,
            width: DEFAULT_NEW_HOTSPOT_WIDTH,
            height: DEFAULT_NEW_HOTSPOT_HEIGHT,
            tooltip: `new hotspot ${hotspots.length + 1}`,
            linkTarget: { type: LinkType.None, value: '' }
        };

        setProp((props: { hotspots: HotspotData[] }) => props.hotspots = [...props.hotspots, newHotspot], 500);
        setSelectedHotspotId(newHotspot.id); // Select the newly added hotspot
        // Ensure other modes are off
        setIsDrawing(false);
        setIsDraggingHotspot(false);
        setIsResizingHotspot(false);
    };


    return (
        <div style={{ userSelect: isDrawing || isDraggingHotspot || isResizingHotspot ? 'none' : 'auto' }}>
            <div className="text-sm text-gray-500 mb-2">
                <p>{intl.formatMessage({ id: 'topic.decoration.imageHotspot.suggestion' })}</p>
                <p>{intl.formatMessage({ id: 'topic.decoration.imageHotspot.instructions' })}</p>
            </div>

            <div className='p-3 rounded-md m-3' style={{ border: '1px solid #140D0D0D' }}>
                <div
                    style={{ /* ... (image container style) ... */
                        cursor: imageUrl ? (isDrawing ? 'crosshair' : 'default') : 'default',
                    }}
                    className='relative'
                    onMouseDown={handleImageMouseDownForDrawing}
                >
                    {/* ... (Image and hotspot rendering - no change in structure, only content) ... */}
                    {imageUrl ? (
                        <img ref={imagePreviewRef} src={imageUrl} alt="Preview" style={{ display: 'block', maxWidth: '100%', opacity: isDrawing ? 0.7 : 1 }} draggable="false" />
                    ) : (
                        <div className='bg-[#f3f5f6] text-gray-500 w-[300px] h-[150px] flex items-center justify-center'>
                            {intl.formatMessage({ id: 'topic.decoration.imageHotspot.upload.placeholder' })}
                        </div>
                    )}
                    {imageUrl && hotspots.map((h: HotspotData, index: number) => (
                        <div
                            key={`preview-${h.id}`}
                            className="existing-hotspot-preview px-1 text-white"
                            onMouseDown={(e) => handleHotspotMouseDownForDragging(e, h)}
                            onClick={(e) => { e.stopPropagation(); if (!isDraggingHotspot && !isResizingHotspot) { setSelectedHotspotId(h.id); setIsDrawing(false); setCurrentRect(null); } }}
                            style={{
                                position: 'absolute',
                                left: `${h.x * 100}%`,
                                top: `${h.y * 100}%`,
                                width: `${h.width * 100}%`,
                                height: `${h.height * 100}%`,
                                border: `2px solid ${selectedHotspotId === h.id ? (isResizingHotspot ? 'rgba(21,91,212,.7)' : 'rgba(21,91,212,.2)') : 'rgba(21, 91, 212, 0.2)'}`,
                                background: selectedHotspotId === h.id ? 'rgba(21,91,212,.9)' : 'rgba(21, 91, 212, 0.2)',
                                boxSizing: 'border-box',
                                cursor: isDraggingHotspot && selectedHotspotId === h.id ? 'grabbing' : (selectedHotspotId === h.id ? 'grab' : 'pointer'),
                                zIndex: selectedHotspotId === h.id ? 10 : 1,
                            }}
                        >
                            {index + 1}
                            {renderResizeHandles(h)}
                        </div>
                    ))}
                    {imageUrl && isDrawing && currentRect && (<div style={{ /* ... (currentRect style) ... */
                        position: 'absolute',
                        left: `${currentRect.x * 100}%`,
                        top: `${currentRect.y * 100}%`,
                        width: `${currentRect.width * 100}%`,
                        height: `${currentRect.height * 100}%`,
                        boxSizing: 'border-box',
                        pointerEvents: 'none',
                        background: 'rgba(0, 255, 0, 0.3)',
                        border: '1px dashed green',

                    }} />)}
                </div>
            </div>

            <div style={{ display: 'flex', gap: '10px', marginBottom: '1rem' }}>
                <Upload name="file" multiple={false} accept=".jpg,.png,.jpeg,.gif,.webp" action="/apigateway/public/upload/object/batch" onChange={handleUploadChange} showUploadList={false}>
                    <Button className='button-outline'>{imageUrl ? intl.formatMessage({ id: 'topic.decoration.imageHotspot.reupload' }) : intl.formatMessage({ id: 'topic.decoration.imageHotspot.upload' })}</Button>
                </Upload>
            </div>


            <h5 className='text-base mt-4 mb-2'>{intl.formatMessage({ id: 'topic.decoration.imageHotspot.hotspotsLinks' })}</h5>
            <Form >
                {hotspots.map((h: HotspotData, index: number) => (
                    <div
                        key={`list-${h.id}`}
                        className='mt-4'
                    >
                        <LinkSettings
                            label={`${intl.formatMessage({ id: 'topic.decoration.imageHotspot.hotspots' })} ${index + 1}`}
                            name={`hotspots.${index}`}
                            onDelete={() => {
                                deleteHotspot(h.id);
                            }}
                            value={h.linkTarget}
                            onChange={(value) => {
                                setProp((props: { hotspots: HotspotData[] }) => {
                                    props.hotspots = props.hotspots.map(hotspot =>
                                        hotspot.id === h.id ? { ...hotspot, linkTarget: value } : hotspot
                                    );
                                }, 500);
                            }}
                        />
                    </div>
                ))}

            </Form>
            <Button className='button-outline mt-4' onClick={handleAddHotspotClick} disabled={!imageUrl}>
                {intl.formatMessage({ id: 'topic.decoration.imageHotspot.addHotspot' })}
            </Button>
        </div>
    );
};

export default ImageHotspotSettings;