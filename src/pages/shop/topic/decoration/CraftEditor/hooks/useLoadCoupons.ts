import { useEffect, useState } from "react";
import { CouponItem, CouponProps } from "../components/Coupon/type";

const queryCouponPage = async (params: any) => {
    console.log('Fetching coupons:', params);
    // Simulate API call
    return new Promise(resolve => setTimeout(() => resolve({
        data: [
            {
                couponId: '123456',
                couponName: '优惠券1',
                couponAmount: '100',
                totalQuantity: 100,
                remainingQuantity: 100,
                validityPeriod: '2023-01-01',
                status: '已使用',
                updater: '张三',
                updateTime: '2023-01-01',
                enableAmount: 100,
            },
            // {
            //     couponId: '123457',
            //     couponName: '优惠券2',
            //     couponAmount: '200',
            //     totalQuantity: 200,
            //     remainingQuantity: 200,
            //     validityPeriod: '2023-01-01',
            //     status: '已使用',
            //     updater: '张三',
            //     updateTime: '2023-01-01',
            //     enableAmount: 200,
            // },
            // {
            //     couponId: '123458',
            //     couponName: '优惠券3',
            //     couponAmount: '300',
            //     totalQuantity: 300,
            //     remainingQuantity: 300,
            //     validityPeriod: '2023-01-01',
            //     status: '已使用',
            //     updater: '张三',
            //     updateTime: '2023-01-01',
            //     enableAmount: 300,
            // },
            // {
            //     couponId: '123459',
            //     couponName: '优惠券4',
            //     couponAmount: '400',
            //     totalQuantity: 400,
            //     remainingQuantity: 400,
            //     validityPeriod: '2023-01-01',
            //     status: '已使用',
            //     updater: '张三',
            //     updateTime: '2023-01-01',
            //     enableAmount: 400,
            // },
        ],
    }), 1000));

};

export default function useLoadCoupons(props: CouponProps) {
    const { couponsIds } = props;

    const [loading, setLoading] = useState(false);
    const [coupons, setCoupons] = useState<CouponItem[]>([]);

    useEffect(() => {
        if (couponsIds.length === 0) return;
        const loadCoupons = async () => {
            setLoading(true);
            const fetched = await queryCouponPage({ ids: couponsIds });
            setCoupons(fetched.data ?? []);
            setLoading(false);
        };
        loadCoupons();
    }, [couponsIds]);

    return {
        loading,
        coupons
    };
}